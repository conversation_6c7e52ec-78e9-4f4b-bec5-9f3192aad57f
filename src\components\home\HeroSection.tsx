import React, { useState, useEffect, useRef, memo } from 'react';
import { Link } from 'react-router-dom';

const HeroSection = memo(() => {
  const [isMobile, setIsMobile] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleCanPlay = () => {
      // Ensure video plays smoothly by preloading
      video.play().catch(console.error);
    };

    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, []);

  return (
    <section className="hero-section">
      {/* Background Video - Only load on desktop for performance */}
      {!isMobile ? (
        <video
          ref={videoRef}
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          poster=""
          style={{
            opacity: 1, // Always show video/poster
            transition: 'opacity 0.5s ease-in-out'
          }}
        >
          <source src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fvideo%20go.webm?alt=media&token=4f246761-6a72-416b-92c8-cfb2d7f53e80"  />
          {/* Fallback for browsers that don't support video */}
          Your browser does not support the video tag.
        </video>
      ) : (
        // Mobile fallback - static background image for better performance
        <video
          ref={videoRef}
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          poster=""
          style={{
            opacity: 1, // Always show video/poster
            transition: 'opacity 0.5s ease-in-out'
          }}
        >
          <source src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fvideo%20go.webm?alt=media&token=4f246761-6a72-416b-92c8-cfb2d7f53e80"  />
          {/* Fallback for browsers that don't support video */}
          Your browser does not support the video tag.
        </video>
      )}

      {/* Video Overlay */}
      <div className="hero-overlay"></div>

      <div className="hero-content">
        {/* Logo and Company Name Combined */}
        <div className="flex flex-col items-center mb-8">
          <img
            src="/photos/heroLogo.svg"
            alt="Warriors of Africa Safari Logo"
            className="hero-logo mb-4"
          />
          <div className="hero-company-name">
            WARRIORS OF <br />
            AFRICA SAFARI
          </div>
        </div>

        {/* Main Headline */}
        <h1 className="hero-headline">
          Welcome to the<br />
          Land of <em>Endless</em><br />
          Safari
        </h1>

        {/* Call-to-Action Button */}
        <Link to="/tour-builder" className="hero-cta-button">
          JOIN THE SAFARI
        </Link>
      </div>
    </section>
  );
});

HeroSection.displayName = 'HeroSection';

export default HeroSection;
